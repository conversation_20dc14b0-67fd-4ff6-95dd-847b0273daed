#!/usr/bin/env python3
"""
测试 LangChain 重写的 answer_question_with_session 函数
"""

import os
import sys
import json
from uuid import uuid4

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from app import (
    session_store, 
    init_session, 
    test_and_store_client,
    answer_question_with_session,
    global_mcp_client,
    global_mcp_tool_wrapper
)

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试 LangChain 重写的基本功能 ===")
    
    # 1. 初始化会话
    session_id = init_session()
    print(f"✅ 会话初始化成功: {session_id}")
    
    # 2. 模拟 API 配置（使用环境变量或默认值）
    api_key = os.environ.get("OPENAI_API_KEY", "test-key")
    base_url = os.environ.get("OPENAI_BASE_URL", "https://api-inference.modelscope.cn/v1")
    model = os.environ.get("OPENAI_MODEL", "Qwen/Qwen3-235B-A22B")
    
    print(f"使用 API 配置:")
    print(f"  Base URL: {base_url}")
    print(f"  Model: {model}")
    print(f"  API Key: {'*' * (len(api_key) - 4) + api_key[-4:] if len(api_key) > 4 else '***'}")
    
    # 3. 测试 API 连接（如果有真实的 API key）
    if api_key != "test-key":
        connection_result = test_and_store_client(api_key, base_url, model, session_id)
        print(f"API 连接测试: {connection_result}")
        
        if "❌" in connection_result:
            print("⚠️ API 连接失败，跳过实际调用测试")
            return False
    else:
        print("⚠️ 使用测试 API key，跳过连接测试")
        # 手动设置会话数据用于测试
        session_store[session_id]["reimbursement_rules"] = [
            {
                "rule_name": "差旅费报销",
                "rule_description": "员工因公出差产生的交通费、住宿费等可以报销",
                "rule_category": "差旅费"
            },
            {
                "rule_name": "办公用品报销", 
                "rule_description": "购买办公用品的费用可以报销",
                "rule_category": "办公用品"
            }
        ]
        return False  # 不进行实际的 LLM 调用测试
    
    # 4. 添加示例报销规则
    session_store[session_id]["reimbursement_rules"] = [
        {
            "rule_name": "差旅费报销",
            "rule_description": "员工因公出差产生的交通费、住宿费等可以报销，需要提供发票和出差申请",
            "rule_category": "差旅费"
        },
        {
            "rule_name": "办公用品报销", 
            "rule_description": "购买办公用品的费用可以报销，单次不超过500元",
            "rule_category": "办公用品"
        },
        {
            "rule_name": "业务招待费",
            "rule_description": "业务招待费用可以报销，需要提供客户信息和业务说明",
            "rule_category": "业务招待"
        }
    ]
    
    print("✅ 报销规则已设置")
    
    # 5. 测试基本问答（无工具调用）
    print("\n--- 测试基本问答 ---")
    history = []
    question = "差旅费报销需要什么材料？"
    
    try:
        _, updated_history = answer_question_with_session(question, history, session_id)
        print(f"问题: {question}")
        print(f"回答: {updated_history[-1]['content'] if updated_history else '无回答'}")
        print("✅ 基本问答测试成功")
        return True
    except Exception as e:
        print(f"❌ 基本问答测试失败: {e}")
        return False

def test_mcp_tools():
    """测试 MCP 工具集成"""
    print("\n=== 测试 MCP 工具集成 ===")
    
    # 检查 MCP 工具包装器
    tools = global_mcp_tool_wrapper.get_tools()
    print(f"可用工具数量: {len(tools)}")
    
    if tools:
        print("可用工具:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        print("✅ MCP 工具集成正常")
        return True
    else:
        print("⚠️ 没有可用的 MCP 工具")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试无会话数据的情况
    fake_session_id = str(uuid4())
    history = []
    question = "测试问题"
    
    try:
        _, updated_history = answer_question_with_session(question, history, fake_session_id)
        if updated_history and "❌" in updated_history[-1]['content']:
            print("✅ 无会话数据错误处理正常")
            return True
        else:
            print("❌ 无会话数据错误处理异常")
            return False
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试 LangChain 重写的 answer_question_with_session 函数")
    print("=" * 60)
    
    results = []
    
    # 测试基本功能
    results.append(test_basic_functionality())
    
    # 测试 MCP 工具集成
    results.append(test_mcp_tools())
    
    # 测试错误处理
    results.append(test_error_handling())
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！LangChain 重写成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
