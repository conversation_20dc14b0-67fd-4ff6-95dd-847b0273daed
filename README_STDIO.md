# 智能助手系统 - stdio版本

这是一个使用stdio协议连接MCP服务器的Gradio应用，支持城市分级查询和发票OCR识别功能。

## 文件结构

```
├── app_stdio.py                 # 主应用程序 (stdio版本)
├── mcp_citytier_stdio.py       # 城市分级查询MCP服务器 (stdio版本)
├── mcp_invoice_stdio.py         # 发票OCR识别MCP服务器 (stdio版本)
├── invoice_core.py              # 发票识别核心模块 (新增)
├── agent_invoice.py             # 发票识别Gradio界面 (重构)
└── upload_files/                # 上传文件目录
```

## 重要更新

### 模块重构
为了解决MCP服务器连接时的卡顿问题，我们将发票识别功能进行了模块化重构：

- **`invoice_core.py`**: 包含核心的 `inference` 函数和所有OCR处理逻辑，支持延迟初始化
- **`agent_invoice.py`**: 只包含Gradio界面，导入 `invoice_core` 模块
- **`mcp_invoice_stdio.py`**: MCP服务器，导入 `invoice_core` 模块

这样避免了在导入时就初始化重量级的AI模型和OCR组件，解决了连接卡顿的问题。

## 与SSE版本的区别

| 特性 | SSE版本 | stdio版本 |
|------|---------|-----------|
| 通信协议 | HTTP SSE | stdio管道 |
| 服务器启动 | 独立进程，监听端口 | 子进程，通过管道通信 |
| 网络依赖 | 需要网络端口 | 无网络依赖 |
| 调试难度 | 容易（可独立测试） | 较难（依赖父进程） |
| 性能 | 网络开销 | 管道通信，更快 |
| 部署复杂度 | 需要管理多个端口 | 单一应用 |

## 功能特性

### 🏙️ 城市分级查询
- 支持查询单个城市的分级信息
- 支持批量查询多个城市
- 支持获取指定分级的所有城市列表
- 涵盖一线到五线城市的完整数据

### 📄 发票OCR识别
- 支持多种发票类型识别
- 支持URL和Base64两种图片输入方式
- 自动提取发票关键字段信息
- 支持批量发票识别

### 🔧 技术特性
- 使用stdio协议进行MCP通信
- 内置文件服务器解决跨进程文件访问
- 智能参数处理和错误恢复
- 完整的日志记录和调试信息

## 安装依赖

```bash
pip install fastmcp gradio openai python-dotenv
```

## 使用方法

### 1. 启动应用

```bash
python app_stdio.py
```

应用将在以下端口启动：
- **7861**: Gradio主应用
- **8889**: 文件服务器

### 2. 连接MCP服务器

在"设置"页面中：

1. **城市分级查询服务器**：
   - 服务器命令: `python mcp_citytier_stdio.py`
   - 服务器名称: `city_server_stdio`
   - 点击"测试连接"验证配置
   - 点击"连接"建立连接

2. **发票OCR识别服务器**：
   - 服务器命令: `python mcp_invoice_stdio.py`
   - 服务器名称: `ocr_server_stdio`
   - 点击"测试连接"验证配置
   - 点击"连接"建立连接

### 3. 使用功能

#### 城市分级查询
在聊天界面输入：
- "查询北京的城市分级"
- "上海属于几线城市？"
- "帮我查询成都、杭州、重庆的城市分级"

#### 发票OCR识别
1. 点击"上传图片"按钮选择发票图片
2. 输入识别请求，如"请识别这张发票"
3. 系统会自动调用OCR工具进行识别

## 配置说明

### API配置
在"设置"页面配置：
- **API Key**: 您的OpenAI兼容API密钥
- **Base URL**: API服务地址
- **Model Name**: 使用的模型名称

### MCP服务器配置
- **服务器命令**: 启动MCP服务器的完整命令
- **服务器名称**: 用于标识服务器的唯一名称

## 工具列表

### 城市分级查询工具
- `query_city_tier(city_name)`: 查询单个城市分级
- `query_multiple_cities(city_list)`: 批量查询城市分级
- `get_tier_cities(tier)`: 获取指定分级的城市列表

### 发票OCR识别工具
- `recognize_single_invoice(image_url, image_data)`: 识别单张发票
- `recognize_multiple_invoices(image_list)`: 批量识别发票
- `get_invoice_template_info()`: 获取支持的发票模板信息

## 日志和调试

应用会生成详细的日志文件：
- `ai_interaction_stdio.log`: 包含所有交互和调试信息

日志内容包括：
- MCP服务器连接状态
- 工具调用参数和结果
- OpenAI API交互记录
- 错误和异常信息

## 故障排除

### 1. MCP服务器连接失败
- 检查Python环境和依赖包
- 确认MCP服务器文件存在且可执行
- 查看日志文件中的详细错误信息

### 2. 发票识别失败
- 确认图片格式支持（jpg, png, gif, bmp）
- 检查文件服务器是否正常运行
- 验证图片文件是否损坏

### 3. 城市查询无结果
- 检查城市名称拼写
- 尝试去掉"市"字后缀
- 查看支持的城市列表

## 开发说明

### 添加新的MCP工具
1. 在相应的MCP服务器文件中添加新的`@mcp.tool()`函数
2. 重启应用并重新连接MCP服务器
3. 新工具将自动可用

### 修改UI界面
编辑`app_stdio.py`中的`gradio_app()`函数来自定义界面。

### 扩展功能
可以创建新的MCP服务器文件，按照现有模式添加更多功能。

## 注意事项

1. **进程管理**: stdio版本的MCP服务器作为子进程运行，应用退出时会自动清理
2. **文件权限**: 确保upload_files目录有正确的读写权限
3. **资源使用**: 每个MCP服务器都会占用一定的系统资源
4. **错误恢复**: 如果MCP服务器崩溃，需要重新连接

## 性能优化

- stdio通信比网络通信更快
- 文件服务器使用独立端口避免冲突
- 智能参数处理减少不必要的数据传输
- 异步处理提高响应速度
